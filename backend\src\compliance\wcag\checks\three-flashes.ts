/**
 * WCAG-046: Three Flashes Check
 * Success Criterion: 2.3.1 Three Flashes or Below Threshold (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';
import MultimediaAccessibilityTester, {
  MultimediaAccessibilityReport,
  MediaAccessibilityResult,
} from '../utils/multimedia-accessibility-tester';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { ModernFrameworkOptimizer } from '../utils/modern-framework-optimizer';

interface FlashDetectionResult {
  flashingElements: number;
  strobeElements: number;
  animationElements: number;
  riskLevel: 'none' | 'low' | 'medium' | 'high' | 'critical';
  seizureRiskScore: number;
  detectedPatterns: string[];
  problematicElements: string[];
}

interface PhotosensitiveAnalysis {
  hasFlashingContent: boolean;
  flashFrequency: number;
  contrastRatio: number;
  affectedArea: number;
  duration: number;
  riskAssessment: 'safe' | 'warning' | 'dangerous';
}

interface StrobePatternDetection {
  strobeDetected: boolean;
  frequency: number;
  pattern: 'regular' | 'irregular' | 'burst' | 'none';
  intensity: number;
  coverage: number;
  riskLevel: 'low' | 'medium' | 'high';
}

export interface ThreeFlashesConfig extends EnhancedCheckConfig {
  enableAdvancedFlashDetection?: boolean;
  enableSeizureRiskAssessment?: boolean;
  enablePhotosensitiveContentAnalysis?: boolean;
  enableStrobePatternDetection?: boolean;
  enableMultimediaAccessibilityTesting?: boolean;
  enableAccessibilityPatterns?: boolean;
}

export class ThreeFlashesCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private multimediaAccessibilityTester = MultimediaAccessibilityTester.getInstance();
  private accessibilityPatternLibrary = AccessibilityPatternLibrary.getInstance();
  private modernFrameworkOptimizer = ModernFrameworkOptimizer.getInstance();

  async performCheck(config: ThreeFlashesConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with specialized flash detection
    const enhancedConfig: ThreeFlashesConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 4000, // Target: <4s performance
      },
      enableAdvancedFlashDetection: true,
      enableSeizureRiskAssessment: true,
      enablePhotosensitiveContentAnalysis: true,
      enableStrobePatternDetection: true,
      enableMultimediaAccessibilityTesting: true,
      enableAccessibilityPatterns: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-046',
      'Three Flashes or Below Threshold',
      'operable',
      0.0458,
      'A',
      enhancedConfig,
      this.executeThreeFlashesCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with flash detection analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-046',
        ruleName: 'Three Flashes or Below Threshold',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.8,
          checkType: 'flash-detection-analysis',
          animationAnalysis: true,
          flashThresholdValidation: true,
          multimediaAccessibilityTesting: enhancedConfig.enableMultimediaAccessibilityTesting,
          advancedFlashDetection: enhancedConfig.enableAdvancedFlashDetection,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 20,
      },
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeThreeFlashesCheck(
    page: Page,
    _config: ThreeFlashesConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Specialized Flash & Strobe Detection Algorithm - Advanced Implementation
    const advancedFlashDetection = await this.executeAdvancedFlashDetection(page);

    // Seizure Risk Assessment Algorithm
    const seizureRiskAssessment = await this.assessSeizureRisk(page);

    // Photosensitive Content Analysis Algorithm
    const photosensitiveAnalysis = await this.analyzePhotosensitiveContent(page);

    // Strobe Pattern Detection Algorithm
    const strobePatternDetection = await this.detectStrobePatterns(page);

    // Enhanced multimedia accessibility testing
    const multimediaReport =
      await this.multimediaAccessibilityTester.testMultimediaAccessibility(page);
    const multimediaFlashAnalysis = await this.analyzeMultimediaFlashContent(
      page,
      multimediaReport,
    );

    // Combine all specialized detection results
    const allAnalyses = [
      advancedFlashDetection,
      seizureRiskAssessment,
      photosensitiveAnalysis,
      strobePatternDetection,
      multimediaFlashAnalysis,
    ];

    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score with 90% accuracy target
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Advanced Flash Detection Algorithm - Core Implementation
   * Target: 90% flash and strobe detection accuracy
   */
  private async executeAdvancedFlashDetection(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const flashDetection = await page.evaluate((): FlashDetectionResult => {
      const flashingElements = document.querySelectorAll(
        '[class*="flash"], [class*="blink"], [class*="strobe"], [class*="flicker"], .animated, .pulse, .heartbeat',
      ).length;

      const strobeElements = document.querySelectorAll(
        '[class*="strobe"], [class*="disco"], [class*="lightning"], [style*="animation"]',
      ).length;

      const animationElements = document.querySelectorAll(
        '.animate, .animated, [style*="animation"], [style*="transition"]',
      ).length;

      // Analyze CSS animations for potential flash patterns
      const detectedPatterns: string[] = [];
      const problematicElements: string[] = [];

      // Check for rapid animations
      const animatedElements = Array.from(document.querySelectorAll('*')).filter((el) => {
        const style = window.getComputedStyle(el);
        const animationDuration = parseFloat(style.animationDuration) || 0;
        const transitionDuration = parseFloat(style.transitionDuration) || 0;

        // Flag animations faster than 0.33s (3Hz threshold)
        if (animationDuration > 0 && animationDuration < 0.33) {
          detectedPatterns.push(`Fast animation: ${animationDuration}s`);
          problematicElements.push(el.tagName.toLowerCase());
          return true;
        }

        if (transitionDuration > 0 && transitionDuration < 0.33) {
          detectedPatterns.push(`Fast transition: ${transitionDuration}s`);
          problematicElements.push(el.tagName.toLowerCase());
          return true;
        }

        return false;
      });

      // Check for opacity/visibility changes
      const flashyElements = Array.from(document.querySelectorAll('*')).filter((el) => {
        const style = window.getComputedStyle(el);
        const animationName = style.animationName;

        if (animationName && animationName !== 'none') {
          // Check if animation involves opacity or visibility changes
          const keyframes = Array.from(document.styleSheets)
            .flatMap((sheet) => {
              try {
                return Array.from(sheet.cssRules || []);
              } catch {
                return [];
              }
            })
            .filter((rule) => rule.type === CSSRule.KEYFRAMES_RULE);

          const relevantKeyframe = keyframes.find((kf) => (kf as CSSKeyframesRule).name === animationName);
          if (relevantKeyframe) {
            const keyframeText = (relevantKeyframe as CSSKeyframesRule).cssText.toLowerCase();
            if (keyframeText.includes('opacity') || keyframeText.includes('visibility')) {
              detectedPatterns.push(`Opacity/visibility animation: ${animationName}`);
              problematicElements.push(el.tagName.toLowerCase());
              return true;
            }
          }
        }

        return false;
      });

      // Calculate risk level
      const totalProblematic = animatedElements.length + flashyElements.length;
      let riskLevel: FlashDetectionResult['riskLevel'] = 'none';
      let seizureRiskScore = 0;

      if (totalProblematic === 0) {
        riskLevel = 'none';
        seizureRiskScore = 0;
      } else if (totalProblematic <= 2) {
        riskLevel = 'low';
        seizureRiskScore = 0.2;
      } else if (totalProblematic <= 5) {
        riskLevel = 'medium';
        seizureRiskScore = 0.5;
      } else if (totalProblematic <= 10) {
        riskLevel = 'high';
        seizureRiskScore = 0.8;
      } else {
        riskLevel = 'critical';
        seizureRiskScore = 1.0;
      }

      return {
        flashingElements,
        strobeElements,
        animationElements,
        riskLevel,
        seizureRiskScore,
        detectedPatterns,
        problematicElements,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (flashDetection.riskLevel === 'none' || flashDetection.riskLevel === 'low') {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Advanced flash detection: Low or no seizure risk detected',
        value: `Risk level: ${flashDetection.riskLevel}, Score: ${(flashDetection.seizureRiskScore * 100).toFixed(1)}%`,
        severity: 'info',
      });
    } else {
      issues.push(`Flash/strobe content detected with ${flashDetection.riskLevel} seizure risk`);
      evidence.push({
        type: 'code',
        description: `Advanced flash detection: ${flashDetection.riskLevel} risk detected`,
        value: `Flashing: ${flashDetection.flashingElements}, Strobe: ${flashDetection.strobeElements}, Animations: ${flashDetection.animationElements}`,
        severity: flashDetection.riskLevel === 'critical' ? 'error' : 'warning',
      });
      recommendations.push('Reduce flash frequency below 3Hz or provide seizure warning');
    }

    // Report detected patterns
    if (flashDetection.detectedPatterns.length > 0) {
      evidence.push({
        type: 'text',
        description: 'Flash patterns detected',
        value: flashDetection.detectedPatterns.slice(0, 3).join('; '),
        severity: 'info',
      });
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Seizure Risk Assessment Algorithm
   */
  private async assessSeizureRisk(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const riskAssessment = await page.evaluate((): PhotosensitiveAnalysis => {
      // Check for video elements
      const videos = Array.from(
        document.querySelectorAll('video, iframe[src*="youtube"], iframe[src*="vimeo"]'),
      );
      const hasFlashingContent = videos.length > 0;

      // Estimate flash frequency (simplified heuristic)
      let flashFrequency = 0;
      const animatedElements = Array.from(document.querySelectorAll('[style*="animation"]'));

      animatedElements.forEach((el) => {
        const style = window.getComputedStyle(el);
        const duration = parseFloat(style.animationDuration) || 1;
        const iterationCount =
          style.animationIterationCount === 'infinite'
            ? 10
            : parseFloat(style.animationIterationCount) || 1;

        if (duration > 0) {
          const frequency = iterationCount / duration;
          flashFrequency = Math.max(flashFrequency, frequency);
        }
      });

      // Check contrast ratios (simplified)
      let contrastRatio = 1;
      const elements = Array.from(document.querySelectorAll('*')).slice(0, 50); // Sample elements
      elements.forEach((el) => {
        const style = window.getComputedStyle(el);
        const bgColor = style.backgroundColor;
        const textColor = style.color;

        // Simplified contrast calculation
        if (bgColor !== 'rgba(0, 0, 0, 0)' && textColor !== 'rgba(0, 0, 0, 0)') {
          // This is a simplified heuristic - real contrast calculation would be more complex
          contrastRatio = Math.max(contrastRatio, 2.5);
        }
      });

      // Calculate affected area (percentage of viewport)
      const viewportArea = window.innerWidth * window.innerHeight;
      let affectedArea = 0;

      animatedElements.forEach((el) => {
        const rect = el.getBoundingClientRect();
        const elementArea = rect.width * rect.height;
        affectedArea += elementArea;
      });

      const affectedPercentage = Math.min((affectedArea / viewportArea) * 100, 100);

      // Estimate duration
      const duration = Math.max(
        ...animatedElements.map((el) => {
          const style = window.getComputedStyle(el);
          return parseFloat(style.animationDuration) || 0;
        }),
        0,
      );

      // Risk assessment
      let riskAssessment: PhotosensitiveAnalysis['riskAssessment'] = 'safe';

      if (flashFrequency > 3 && affectedPercentage > 25) {
        riskAssessment = 'dangerous';
      } else if (flashFrequency > 3 || affectedPercentage > 25) {
        riskAssessment = 'warning';
      }

      return {
        hasFlashingContent,
        flashFrequency,
        contrastRatio,
        affectedArea: affectedPercentage,
        duration,
        riskAssessment,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (riskAssessment.riskAssessment === 'safe') {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Seizure risk assessment: Content is safe',
        value: `Flash frequency: ${riskAssessment.flashFrequency.toFixed(1)}Hz, Affected area: ${riskAssessment.affectedArea.toFixed(1)}%`,
        severity: 'info',
      });
    } else {
      issues.push(`Seizure risk assessment: ${riskAssessment.riskAssessment} content detected`);
      evidence.push({
        type: 'code',
        description: `Seizure risk assessment: ${riskAssessment.riskAssessment} risk`,
        value: `Flash frequency: ${riskAssessment.flashFrequency.toFixed(1)}Hz, Affected area: ${riskAssessment.affectedArea.toFixed(1)}%, Duration: ${riskAssessment.duration}s`,
        severity: riskAssessment.riskAssessment === 'dangerous' ? 'error' : 'warning',
      });
      recommendations.push(
        'Reduce flash frequency below 3Hz or limit affected screen area below 25%',
      );
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Photosensitive Content Analysis Algorithm
   */
  private async analyzePhotosensitiveContent(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const photosensitiveAnalysis = await page.$$eval(
      'video, canvas, svg, img[src*="gif"]',
      (elements) => {
        return elements.map((element, index) => {
          const tagName = element.tagName.toLowerCase();
          const isVideo = tagName === 'video';
          const isCanvas = tagName === 'canvas';
          const isAnimatedGif = tagName === 'img' && element.getAttribute('src')?.includes('gif');
          const isSvg = tagName === 'svg';

          // Check for warning attributes or classes
          const hasWarning =
            element.hasAttribute('data-seizure-warning') ||
            element.classList.contains('seizure-warning') ||
            element.closest('[data-seizure-warning]') !== null;

          // Check for controls
          const hasControls = isVideo ? (element as HTMLVideoElement).controls : false;
          const hasAutoplay = isVideo ? (element as HTMLVideoElement).autoplay : false;

          return {
            index,
            tagName,
            selector: `${tagName}:nth-of-type(${index + 1})`,
            isVideo,
            isCanvas,
            isAnimatedGif,
            isSvg,
            hasWarning,
            hasControls,
            hasAutoplay,
            isPotentiallyProblematic: (isVideo && hasAutoplay) || isAnimatedGif || isCanvas,
          };
        });
      },
    );

    const totalChecks = photosensitiveAnalysis.length;
    let passedChecks = 0;

    photosensitiveAnalysis.forEach((element, index) => {
      if (!element.isPotentiallyProblematic || element.hasWarning) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Photosensitive content ${index + 1} is properly handled`,
          value: `${element.selector} - ${element.hasWarning ? 'has warning' : 'not problematic'}`,
          severity: 'info',
        });
      } else {
        issues.push(`Photosensitive content ${index + 1} lacks proper warnings`);
        evidence.push({
          type: 'code',
          description: `Photosensitive content ${index + 1} needs seizure warning`,
          value: `${element.selector} - ${element.tagName} without warning (autoplay: ${element.hasAutoplay})`,
          severity: 'warning',
        });
        recommendations.push(`Add seizure warning to ${element.tagName} element ${index + 1}`);
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Strobe Pattern Detection Algorithm
   */
  private async detectStrobePatterns(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const strobeDetection = await page.evaluate((): StrobePatternDetection => {
      // Look for strobe-like patterns in CSS animations
      const strobeKeywords = [
        'strobe',
        'flash',
        'blink',
        'pulse',
        'heartbeat',
        'disco',
        'lightning',
      ];
      const strobeElements = Array.from(document.querySelectorAll('*')).filter((el) => {
        const className = el.className.toString().toLowerCase();
        const animationName = window.getComputedStyle(el).animationName.toLowerCase();

        return strobeKeywords.some(
          (keyword) => className.includes(keyword) || animationName.includes(keyword),
        );
      });

      const strobeDetected = strobeElements.length > 0;

      // Analyze animation patterns
      let frequency = 0;
      let pattern: StrobePatternDetection['pattern'] = 'none';
      let intensity = 0;
      let coverage = 0;

      if (strobeDetected) {
        // Calculate average frequency
        const frequencies = strobeElements.map((el) => {
          const style = window.getComputedStyle(el);
          const duration = parseFloat(style.animationDuration) || 1;
          return 1 / duration; // Hz
        });

        frequency = frequencies.reduce((a, b) => a + b, 0) / frequencies.length;

        // Determine pattern type
        const regularPattern = frequencies.every((f) => Math.abs(f - frequency) < 0.5);
        if (regularPattern) {
          pattern = 'regular';
        } else if (frequency > 5) {
          pattern = 'burst';
        } else {
          pattern = 'irregular';
        }

        // Calculate intensity (simplified)
        intensity = Math.min(frequency / 10, 1); // Normalize to 0-1

        // Calculate coverage
        const viewportArea = window.innerWidth * window.innerHeight;
        const strobeArea = strobeElements.reduce((total, el) => {
          const rect = el.getBoundingClientRect();
          return total + rect.width * rect.height;
        }, 0);

        coverage = Math.min((strobeArea / viewportArea) * 100, 100);
      }

      // Determine risk level
      let riskLevel: StrobePatternDetection['riskLevel'] = 'low';
      if (frequency > 3 && coverage > 25) {
        riskLevel = 'high';
      } else if (frequency > 3 || coverage > 25) {
        riskLevel = 'medium';
      }

      return {
        strobeDetected,
        frequency,
        pattern,
        intensity,
        coverage,
        riskLevel,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (!strobeDetection.strobeDetected || strobeDetection.riskLevel === 'low') {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Strobe pattern detection: No dangerous strobe patterns detected',
        value: `Frequency: ${strobeDetection.frequency.toFixed(1)}Hz, Coverage: ${strobeDetection.coverage.toFixed(1)}%`,
        severity: 'info',
      });
    } else {
      issues.push(`Dangerous strobe patterns detected (${strobeDetection.riskLevel} risk)`);
      evidence.push({
        type: 'code',
        description: `Strobe pattern detection: ${strobeDetection.riskLevel} risk strobe patterns`,
        value: `Pattern: ${strobeDetection.pattern}, Frequency: ${strobeDetection.frequency.toFixed(1)}Hz, Coverage: ${strobeDetection.coverage.toFixed(1)}%`,
        severity: strobeDetection.riskLevel === 'high' ? 'error' : 'warning',
      });
      recommendations.push('Reduce strobe frequency below 3Hz or limit coverage below 25%');
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Enhanced multimedia flash content analysis
   */
  private async analyzeMultimediaFlashContent(
    page: Page,
    multimediaReport: MultimediaAccessibilityReport,
  ) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const totalChecks =
      multimediaReport.videoElements.length + multimediaReport.audioElements.length;
    let passedChecks = 0;

    // Analyze video elements for flash content
    multimediaReport.videoElements.forEach((video, index) => {
      if (video.controls && !video.autoplay) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Video ${index + 1} has proper controls and no autoplay`,
          value: `${video.selector} - user controlled playback`,
          severity: 'info',
        });
      } else {
        issues.push(`Video ${index + 1} may contain uncontrolled flashing content`);
        evidence.push({
          type: 'code',
          description: `Video ${index + 1} needs user controls`,
          value: `${video.selector} - autoplay: ${video.autoplay}, controls: ${video.controls}`,
          severity: 'warning',
        });
        recommendations.push(`Add controls and disable autoplay for video ${index + 1}`);
      }
    });

    // Audio elements are generally safe from seizure perspective
    passedChecks += multimediaReport.audioElements.length;

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  private async analyzeAnimationFlashRisks(
    page: Page,
    multimediaReport: MultimediaAccessibilityReport,
  ) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Combine audio and video elements for analysis
    const allMediaElements = [...multimediaReport.audioElements, ...multimediaReport.videoElements];
    const totalChecks = allMediaElements.length;
    let passedChecks = 0;

    allMediaElements.forEach((media, index) => {
      const mediaType = media.type;

      // Check for photosensitive content warnings or flash detection
      const hasPhotosensitiveWarning = this.checkPhotosensitiveWarnings(media);
      const hasFlashContent = this.detectFlashContent(media);

      if (hasFlashContent && !hasPhotosensitiveWarning) {
        issues.push(`${mediaType} ${index + 1} may contain flashing content without warning`);
        evidence.push({
          type: 'code',
          description: `${mediaType} ${index + 1} requires photosensitive content warning`,
          value: `${media.selector} - potential flash content detected`,
          selector: media.selector,
          severity: 'error',
        });
        recommendations.push(
          `Add photosensitive content warning or remove flashing from ${mediaType} ${index + 1}`,
        );
      } else {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `${mediaType} ${index + 1} passes flash/strobe requirements`,
          value: `${media.selector} - hasFlashContent: ${hasFlashContent}, hasWarning: ${hasPhotosensitiveWarning}`,
          selector: media.selector,
          severity: 'info',
        });
      }

      // Add issues from MultimediaAccessibilityTester
      if (media.issues.length > 0) {
        media.issues.forEach((issue) => {
          issues.push(`${mediaType} ${index + 1}: ${issue}`);
        });
      }

      // Add recommendations from MultimediaAccessibilityTester
      if (media.recommendations.length > 0) {
        media.recommendations.forEach((recommendation) => {
          recommendations.push(`${mediaType} ${index + 1}: ${recommendation}`);
        });
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze CSS animations and visual effects for flash risks
   */
  private async analyzeCSSAnimationFlashRisks(page: Page) {
    const startTime = Date.now();
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check for CSS animations that might cause flashing
    const flashingAnalysis = await page.evaluate(() => {
      const flashingElements: Array<{
        type: string;
        element: string;
        selector: string;
        flashRate?: number;
        animationName?: string;
        description: string;
        severity: 'high' | 'medium' | 'low';
      }> = [];

      // Check for CSS animations that might cause flashing
      const animatedElements = document.querySelectorAll('*');
      animatedElements.forEach((element, index) => {
        const computedStyle = window.getComputedStyle(element);
        const animationName = computedStyle.animationName;
        const animationDuration = parseFloat(computedStyle.animationDuration) || 0;
        const animationIterationCount = computedStyle.animationIterationCount;

        if (animationName !== 'none' && animationDuration > 0) {
          // Calculate potential flash rate
          const flashRate = animationIterationCount === 'infinite' ? 1 / animationDuration : 0;

          // Check for potentially problematic animations
          if (
            flashRate > 3 ||
            animationName.toLowerCase().includes('flash') ||
            animationName.toLowerCase().includes('blink') ||
            animationName.toLowerCase().includes('strobe')
          ) {
            flashingElements.push({
              type: 'css_animation',
              element: element.tagName.toLowerCase(),
              selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
              flashRate,
              animationName,
              description: `CSS animation "${animationName}" with potential flash rate: ${flashRate.toFixed(2)}/sec`,
              severity: flashRate > 3 ? 'high' : 'medium',
            });
          }
        }
      });

      // Check for blink elements
      const blinkElements = document.querySelectorAll('blink, .blink');
      blinkElements.forEach((element, index) => {
        flashingElements.push({
          type: 'blink_element',
          element: element.tagName.toLowerCase(),
          selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          description: 'Blinking element detected',
          severity: 'high',
        });
      });

      // Check for elements with flashing CSS classes or styles
      const flashingSelectors = [
        '[style*="blink"]',
        '.flash',
        '.flashing',
        '.strobe',
        '.pulse',
        '[class*="flash"]',
        '[class*="blink"]',
        '[class*="strobe"]',
      ];

      flashingSelectors.forEach((selector) => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element, index) => {
          flashingElements.push({
            type: 'flashing_style',
            element: element.tagName.toLowerCase(),
            selector: `${selector}:nth-of-type(${index + 1})`,
            description: `Element with flashing style: ${selector}`,
            severity: 'medium',
          });
        });
      });

      // Check for video elements that might contain flashing content
      const videoElements = document.querySelectorAll('video');
      videoElements.forEach((element, index) => {
        // Note: We can't analyze video content directly, but we can flag for manual review
        flashingElements.push({
          type: 'video_content',
          element: 'video',
          selector: `video:nth-of-type(${index + 1})`,
          description: 'Video element detected - requires manual review for flashing content',
          severity: 'low',
        });
      });

      // Check for canvas elements that might contain flashing animations
      const canvasElements = document.querySelectorAll('canvas');
      canvasElements.forEach((element, index) => {
        flashingElements.push({
          type: 'canvas_content',
          element: 'canvas',
          selector: `canvas:nth-of-type(${index + 1})`,
          description: 'Canvas element detected - requires manual review for flashing content',
          severity: 'low',
        });
      });

      // Check for JavaScript-based flashing (common patterns)
      const scripts = document.querySelectorAll('script');
      let hasFlashingJS = false;
      scripts.forEach((script) => {
        const content = script.textContent || '';
        if (
          content.includes('setInterval') &&
          (content.includes('flash') ||
            content.includes('blink') ||
            content.includes('strobe') ||
            content.includes('opacity'))
        ) {
          hasFlashingJS = true;
        }
      });

      if (hasFlashingJS) {
        flashingElements.push({
          type: 'javascript_flashing',
          element: 'script',
          selector: 'script',
          description: 'JavaScript code detected that may cause flashing',
          severity: 'medium',
        });
      }

      return {
        flashingElements,
        totalElements: flashingElements.length,
        highSeverityCount: flashingElements.filter((el) => el.severity === 'high').length,
        mediumSeverityCount: flashingElements.filter((el) => el.severity === 'medium').length,
        lowSeverityCount: flashingElements.filter((el) => el.severity === 'low').length,
      };
    });

    let score = 100;
    const elementCount = flashingAnalysis.totalElements;
    const scanDuration = Date.now() - startTime;

    if (elementCount > 0) {
      // High severity issues (definite violations) - major score reduction
      if (flashingAnalysis.highSeverityCount > 0) {
        score = Math.max(0, score - flashingAnalysis.highSeverityCount * 40);
        issues.push(
          `${flashingAnalysis.highSeverityCount} elements with high-risk flashing detected`,
        );
      }

      // Medium severity issues (potential violations) - moderate score reduction
      if (flashingAnalysis.mediumSeverityCount > 0) {
        score = Math.max(0, score - flashingAnalysis.mediumSeverityCount * 20);
        issues.push(
          `${flashingAnalysis.mediumSeverityCount} elements with potential flashing detected`,
        );
      }

      // Low severity issues (requires manual review) - minor score reduction
      if (flashingAnalysis.lowSeverityCount > 0) {
        score = Math.max(0, score - flashingAnalysis.lowSeverityCount * 5);
        issues.push(
          `${flashingAnalysis.lowSeverityCount} elements require manual review for flashing content`,
        );
      }

      flashingAnalysis.flashingElements.forEach((element) => {
        evidence.push({
          type: 'code',
          description: `Potential flashing content: ${element.description}`,
          value: element.flashRate
            ? `Flash rate: ${element.flashRate.toFixed(2)} flashes/second`
            : 'Flashing content detected',
          selector: element.selector,
          severity:
            element.severity === 'high'
              ? 'error'
              : element.severity === 'medium'
                ? 'warning'
                : 'info',
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              flashingType: element.type,
              severity: element.severity,
              flashRate: element.flashRate || 0,
              animationName: element.animationName || 'unknown',
            },
          },
        });
      });

      recommendations.push('Remove or modify content that flashes more than 3 times per second');
      recommendations.push('Use fade transitions instead of abrupt flashing');
      recommendations.push('Provide user controls to disable animations');
      recommendations.push('Test video and canvas content manually for flashing');
      recommendations.push('Consider users with photosensitive epilepsy');
    }

    const totalChecks = flashingAnalysis.totalElements;
    const passedChecks =
      totalChecks - flashingAnalysis.highSeverityCount - flashingAnalysis.mediumSeverityCount;

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(type: string): string {
    switch (type) {
      case 'css_animation':
        return '@keyframes flash { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }';
      case 'blink_element':
        return '<blink>Flashing text</blink>';
      case 'flashing_style':
        return '<div class="flash">Flashing content</div>';
      case 'javascript_flashing':
        return 'setInterval(() => { element.style.opacity = element.style.opacity === "0" ? "1" : "0"; }, 100);';
      default:
        return 'Flashing content';
    }
  }

  private getAfterExample(type: string): string {
    switch (type) {
      case 'css_animation':
        return '@keyframes gentle-fade { 0% { opacity: 0.8; } 100% { opacity: 1; } }';
      case 'blink_element':
        return '<span class="highlight">Important text</span>';
      case 'flashing_style':
        return '<div class="gentle-highlight">Non-flashing content</div>';
      case 'javascript_flashing':
        return '// Use gentle transitions instead of rapid flashing\nelement.style.transition = "opacity 1s ease";';
      default:
        return 'Non-flashing content';
    }
  }

  private getFixDescription(type: string): string {
    switch (type) {
      case 'css_animation':
        return 'Modify animation to flash 3 times per second or less';
      case 'blink_element':
        return 'Replace blink element with static highlighting';
      case 'flashing_style':
        return 'Use gentle transitions instead of rapid flashing';
      case 'javascript_flashing':
        return 'Reduce flash frequency to 3 times per second or less';
      case 'video_content':
        return 'Review video content for flashing sequences';
      case 'canvas_content':
        return 'Review canvas animations for flashing content';
      default:
        return 'Eliminate or reduce flashing to safe levels';
    }
  }

  private getCodeExample(type: string): string {
    switch (type) {
      case 'css_animation':
        return `
/* Before: Rapid flashing animation */
@keyframes flash {
  0% { opacity: 1; }
  50% { opacity: 0; }
  100% { opacity: 1; }
}
.flashing { animation: flash 0.2s infinite; }

/* After: Gentle, safe animation */
@keyframes gentle-pulse {
  0% { opacity: 0.8; }
  50% { opacity: 1; }
  100% { opacity: 0.8; }
}
.gentle { animation: gentle-pulse 2s ease-in-out infinite; }
        `;
      case 'blink_element':
        return `
<!-- Before: Blinking element -->
<blink>Important message</blink>

<!-- After: Static highlighting -->
<span class="highlight" style="background-color: yellow; font-weight: bold;">
  Important message
</span>
        `;
      case 'javascript_flashing':
        return `
// Before: Rapid flashing
setInterval(() => {
  element.style.opacity = element.style.opacity === "0" ? "1" : "0";
}, 100); // 10 flashes per second - DANGEROUS

// After: Safe, gentle transition
element.style.transition = "opacity 1s ease-in-out";
setInterval(() => {
  element.style.opacity = element.style.opacity === "0.5" ? "1" : "0.5";
}, 2000); // 0.5 flashes per second - SAFE
        `;
      default:
        return 'Use gentle transitions and avoid rapid flashing';
    }
  }

  /**
   * Check for photosensitive content warnings in media
   */
  private checkPhotosensitiveWarnings(media: MediaAccessibilityResult): boolean {
    // Check for common warning indicators
    const warningIndicators = [
      'photosensitive',
      'epilepsy',
      'seizure',
      'flashing',
      'strobe',
      'warning',
      'caution',
    ];

    // Check in media attributes, surrounding text, or metadata
    const selector = media.selector.toLowerCase();
    const hasWarningInSelector = warningIndicators.some((indicator) =>
      selector.includes(indicator),
    );

    // Additional checks could be added here for aria-labels, titles, etc.
    return hasWarningInSelector;
  }

  /**
   * Detect potential flash content in media
   */
  private detectFlashContent(media: MediaAccessibilityResult): boolean {
    // Check for flash-related indicators in media properties
    const flashIndicators = ['flash', 'strobe', 'blink', 'rapid', 'seizure'];

    const selector = media.selector.toLowerCase();
    const hasFlashIndicators = flashIndicators.some((indicator) => selector.includes(indicator));

    // For now, we use heuristics. In a real implementation, this could
    // integrate with video analysis libraries to detect actual flashing
    return hasFlashIndicators;
  }
}
